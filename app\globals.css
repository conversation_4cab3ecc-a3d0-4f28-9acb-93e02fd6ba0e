@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 51, 51, 53;
  --background-end-rgb: 26, 26, 28;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-end-rgb));
  /* Make all content non-selectable by default */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Click effect animation */
.click-effect {
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: translate(-50%, -50%) scale(0);
  animation: click-ripple 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes click-ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.5;
  }
  100% {
    width: 500px;
    height: 500px;
    opacity: 0;
  }
}

/* Floating word styling */
.floating-word {
  cursor: pointer;
  transform-origin: center;
  position: relative;
  display: inline-block;
  will-change: transform;
  font-size: 0.875rem; /* Default size for mobile */
}

@media (min-width: 768px) {
  .floating-word {
    font-size: 1rem; /* Medium size for tablet */
  }
}

@media (min-width: 1024px) {
  .floating-word {
    font-size: 1.25rem; /* Larger size for desktop */
  }
}

/* Link microinteractions */
.link-item {
  transition: all 0.3s ease;
}

.link-item:hover {
  color: rgba(255, 255, 255, 1);
  letter-spacing: 0.05em;
}

.link-item:active {
  transform: translateY(1px);
}

/* Footer link underline animations */
@keyframes underlineIn {
  0% {
    left: 0;
    right: 100%;
    width: 0;
  }
  100% {
    left: 0;
    right: 0;
    width: 100%;
  }
}

@keyframes underlineOut {
  0% {
    left: 0;
    right: 0;
    width: 100%;
  }
  100% {
    left: 0;
    right: 100%;
    width: 0;
  }
}

.animate-underline-in {
  animation: underlineIn 1.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}

.animate-underline-out {
  animation: underlineOut 1.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}

/* Separate fade animation */
@keyframes fade-in-out {
  0%,
  100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0;
  }
}

.animate-fade {
  animation: fade-in-out 5s ease-in-out infinite;
}

/* Enhanced floating animations with more curved paths - MOVEMENT ONLY */
/* Constrained to stay within boundaries */
@keyframes float-path-1 {
  0% {
    transform: translate(0, 0);
  }
  8% {
    transform: translate(60px, -80px);
  }
  16% {
    transform: translate(120px, -100px);
  }
  24% {
    transform: translate(180px, -60px);
  }
  32% {
    transform: translate(200px, 20px);
  }
  40% {
    transform: translate(180px, 80px);
  }
  48% {
    transform: translate(120px, 100px);
  }
  56% {
    transform: translate(60px, 80px);
  }
  64% {
    transform: translate(30px, 50px);
  }
  72% {
    transform: translate(0px, 0px);
  }
  80% {
    transform: translate(-30px, -20px);
  }
  88% {
    transform: translate(-50px, -40px);
  }
  96% {
    transform: translate(-20px, -20px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-path-2 {
  0% {
    transform: translate(0, 0);
  }
  7% {
    transform: translate(-80px, -50px);
  }
  14% {
    transform: translate(-120px, -70px);
  }
  21% {
    transform: translate(-150px, -40px);
  }
  28% {
    transform: translate(-160px, 20px);
  }
  35% {
    transform: translate(-140px, 80px);
  }
  42% {
    transform: translate(-100px, 120px);
  }
  50% {
    transform: translate(-40px, 140px);
  }
  57% {
    transform: translate(40px, 120px);
  }
  64% {
    transform: translate(100px, 80px);
  }
  71% {
    transform: translate(140px, 20px);
  }
  78% {
    transform: translate(150px, -40px);
  }
  85% {
    transform: translate(120px, -70px);
  }
  92% {
    transform: translate(60px, -50px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-path-3 {
  0% {
    transform: translate(0, 0);
  }
  8% {
    transform: translate(-60px, -80px);
  }
  16% {
    transform: translate(-100px, -120px);
  }
  24% {
    transform: translate(-150px, -100px);
  }
  32% {
    transform: translate(-180px, -50px);
  }
  40% {
    transform: translate(-200px, 30px);
  }
  48% {
    transform: translate(-180px, 100px);
  }
  56% {
    transform: translate(-120px, 140px);
  }
  64% {
    transform: translate(-60px, 100px);
  }
  72% {
    transform: translate(-30px, 50px);
  }
  80% {
    transform: translate(0px, 0px);
  }
  88% {
    transform: translate(-20px, -40px);
  }
  96% {
    transform: translate(-10px, -20px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-path-4 {
  0% {
    transform: translate(0, 0);
  }
  9% {
    transform: translate(20px, -80px);
  }
  18% {
    transform: translate(15px, -140px);
  }
  27% {
    transform: translate(-10px, -180px);
  }
  36% {
    transform: translate(-40px, -140px);
  }
  45% {
    transform: translate(-50px, -80px);
  }
  54% {
    transform: translate(-40px, -30px);
  }
  63% {
    transform: translate(-10px, 80px);
  }
  72% {
    transform: translate(15px, 140px);
  }
  81% {
    transform: translate(40px, 180px);
  }
  90% {
    transform: translate(20px, 100px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-path-5 {
  0% {
    transform: translate(0, 0);
  }
  8% {
    transform: translate(-60px, -50px);
  }
  16% {
    transform: translate(-100px, -90px);
  }
  24% {
    transform: translate(-120px, -140px);
  }
  32% {
    transform: translate(-80px, -180px);
  }
  40% {
    transform: translate(-40px, -200px);
  }
  48% {
    transform: translate(30px, -180px);
  }
  56% {
    transform: translate(80px, -140px);
  }
  64% {
    transform: translate(120px, -100px);
  }
  72% {
    transform: translate(140px, -60px);
  }
  80% {
    transform: translate(100px, -30px);
  }
  88% {
    transform: translate(60px, -20px);
  }
  96% {
    transform: translate(30px, -10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-path-6 {
  0% {
    transform: translate(0, 0);
  }
  8% {
    transform: translate(50px, 30px);
  }
  16% {
    transform: translate(90px, 70px);
  }
  24% {
    transform: translate(120px, 120px);
  }
  32% {
    transform: translate(100px, 160px);
  }
  40% {
    transform: translate(60px, 180px);
  }
  48% {
    transform: translate(0px, 200px);
  }
  56% {
    transform: translate(-60px, 180px);
  }
  64% {
    transform: translate(-100px, 160px);
  }
  72% {
    transform: translate(-120px, 120px);
  }
  80% {
    transform: translate(-90px, 70px);
  }
  88% {
    transform: translate(-50px, 30px);
  }
  96% {
    transform: translate(-25px, 15px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-path-7 {
  0% {
    transform: translate(0, 0);
  }
  7% {
    transform: translate(70px, 20px);
  }
  14% {
    transform: translate(120px, 50px);
  }
  21% {
    transform: translate(150px, 90px);
  }
  28% {
    transform: translate(130px, 120px);
  }
  35% {
    transform: translate(80px, 140px);
  }
  42% {
    transform: translate(30px, 120px);
  }
  50% {
    transform: translate(-30px, 90px);
  }
  57% {
    transform: translate(-80px, 50px);
  }
  64% {
    transform: translate(-120px, 20px);
  }
  71% {
    transform: translate(-150px, 0px);
  }
  78% {
    transform: translate(-130px, -20px);
  }
  85% {
    transform: translate(-80px, -15px);
  }
  92% {
    transform: translate(-30px, -10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-path-8 {
  0% {
    transform: translate(0, 0);
  }
  8% {
    transform: translate(-50px, -60px);
  }
  16% {
    transform: translate(-80px, -100px);
  }
  24% {
    transform: translate(-100px, -140px);
  }
  32% {
    transform: translate(-140px, -120px);
  }
  40% {
    transform: translate(-160px, -80px);
  }
  48% {
    transform: translate(-180px, -30px);
  }
  56% {
    transform: translate(-160px, 30px);
  }
  64% {
    transform: translate(-140px, 80px);
  }
  72% {
    transform: translate(-100px, 120px);
  }
  80% {
    transform: translate(-60px, 140px);
  }
  88% {
    transform: translate(-30px, 80px);
  }
  96% {
    transform: translate(-15px, 40px);
  }
  100% {
    transform: translate(0, 0);
  }
}

/* Mobile-friendly animations with reduced movement */
@media (max-width: 767px) {
  \
  @keyframes float-path-1 
  ,
  @keyframes float-path-2 
  ,
  @keyframes float-path-3 
  ,
  @keyframes float-path-4 
  ,
  @keyframes float-path-5 
  ,
  @keyframes float-path-6 
  ,
  @keyframes float-path-7 
  ,
  @keyframes float-path-8 {
    0%,
    100% {
      transform: translate(0, 0);
    }
    50% {
      transform: translate(0, 15px);
    }
  }
}

/* Update the animation classes to use linear timing and ensure proper stacking */
.animate-float-path-1 {
  animation: float-path-1 30s linear infinite;
}

.animate-float-path-2 {
  animation: float-path-2 35s linear infinite;
}

.animate-float-path-3 {
  animation: float-path-3 40s linear infinite;
}

.animate-float-path-4 {
  animation: float-path-4 32s linear infinite;
}

.animate-float-path-5 {
  animation: float-path-5 38s linear infinite;
}

.animate-float-path-6 {
  animation: float-path-6 36s linear infinite;
}

.animate-float-path-7 {
  animation: float-path-7 42s linear infinite;
}

.animate-float-path-8 {
  animation: float-path-8 34s linear infinite;
}

/* Remove the separate animate-fade class and create combined animation classes */
.floating-word-1 {
  animation: float-path-1 30s linear infinite, fade-in-out 8s ease-in-out infinite;
}

.floating-word-2 {
  animation: float-path-2 35s linear infinite, fade-in-out 8s ease-in-out infinite -2s;
}

.floating-word-3 {
  animation: float-path-3 40s linear infinite, fade-in-out 8s ease-in-out infinite -4s;
}

.floating-word-4 {
  animation: float-path-4 32s linear infinite, fade-in-out 8s ease-in-out infinite -1s;
}

.floating-word-5 {
  animation: float-path-5 38s linear infinite, fade-in-out 8s ease-in-out infinite -3s;
}

.floating-word-6 {
  animation: float-path-6 36s linear infinite, fade-in-out 8s ease-in-out infinite -5s;
}

.floating-word-7 {
  animation: float-path-7 42s linear infinite, fade-in-out 8s ease-in-out infinite -2.5s;
}

.floating-word-8 {
  animation: float-path-8 34s linear infinite, fade-in-out 8s ease-in-out infinite -6s;
}

.floating-word-9 {
  animation: float-path-1 33s linear infinite, fade-in-out 8s ease-in-out infinite -1.5s;
}

.floating-word-10 {
  animation: float-path-2 37s linear infinite, fade-in-out 8s ease-in-out infinite -3.5s;
}

.floating-word-11 {
  animation: float-path-3 39s linear infinite, fade-in-out 8s ease-in-out infinite -4.5s;
}

.floating-word-12 {
  animation: float-path-4 31s linear infinite, fade-in-out 8s ease-in-out infinite -2.8s;
}

.floating-word-13 {
  animation: float-path-5 41s linear infinite, fade-in-out 8s ease-in-out infinite -5.2s;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
