import { Cormorant, Montser<PERSON> } from "next/font/google";
import "./globals.css";

// Using Cormorant for the elegant serif look and <PERSON><PERSON><PERSON> for the clean sans-serif
const cormorant = Cormorant({
  subsets: ["latin"],
  variable: "--font-cormorant",
  display: "swap",
  weight: ["300", "400", "500", "600"],
});

const montserrat = Montserrat({
  subsets: ["latin"],
  variable: "--font-montserrat",
  display: "swap",
  weight: ["300", "400", "500"],
});

export const metadata = {
  title: "The Ivory Bliss | Designed to Celebrate",
  description: "Luxury celebration design and planning",
  generator: "v0.dev",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${cormorant.variable} ${montserrat.variable} font-sans`}
      >
        {children}
      </body>
    </html>
  );
}
