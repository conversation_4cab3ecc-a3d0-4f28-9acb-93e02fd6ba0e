@tailwind base;
@tailwind components;
@tailwind utilities;

/* React PageFlip Book Styles - Single Page Mode */
.book-container {
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 2000px; /* Enhanced perspective for single page flipping */
  width: 100%;
  height: 100%;
}

.flipbook {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5); /* Enhanced shadow for single page */
  border-radius: 12px;
  max-width: 100%;
  max-height: 100%;
  transition: all 0.3s ease-in-out;
}

/* Single page book specific styles */
.single-page-book {
  transform-style: preserve-3d;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.single-page-book .stf__parent {
  border-radius: 12px;
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
}

/* Enhanced page turning animation */
.single-page-book .stf__parent .stf__block {
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Page curl effect */
.single-page-book .stf__parent .stf__block .stf__item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(-45deg, transparent 50%, rgba(0, 0, 0, 0.05) 50%);
  pointer-events: none;
  z-index: 10;
}

/* Page styles */
.page {
  background: white;
  display: flex;
  flex-direction: column;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.page-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.page-header {
  position: absolute;
  top: 10px;
  right: 15px;
  z-index: 10;
}

.page-number {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.5);
  font-weight: 300;
}

.page-body {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.pdf-page-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.pdf-page-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

/* React PageFlip hover effects - Enhanced for single page */
.flipbook:hover {
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.6);
  transform: translateY(-2px);
}

/* Page corner styling - Enhanced for single page */
.stf__parent .stf__block .stf__item {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  transition: all 0.4s ease-in-out;
}

/* Page shadow effects - Enhanced for single page */
.stf__parent .stf__block .stf__item:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}

/* Single page flip animation enhancements */
.single-page-book .stf__parent .stf__block .stf__item {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Page flip shadow for single page mode */
.single-page-book .stf__parent .stf__block .stf__item.--left {
  box-shadow:
    inset -5px 0 10px rgba(0, 0, 0, 0.1),
    0 5px 15px rgba(0, 0, 0, 0.2);
}

.single-page-book .stf__parent .stf__block .stf__item.--right {
  box-shadow:
    inset 5px 0 10px rgba(0, 0, 0, 0.1),
    0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Turn.js Flipbook Styles */
.flipbook-container {
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 2000px;
  width: 100%;
  height: 100%;
}

.flipbook {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  transition: all 0.3s ease-in-out;
}

.flipbook:hover {
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.6);
  transform: translateY(-2px);
}

/* Turn.js page styles */
.flipbook .page {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Turn.js shadow effects */
.flipbook .turn-page {
  background-color: white;
}

.flipbook .shadow {
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%
  );
}

/* Enhanced page turning effects */
.flipbook .hard {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments for turn.js flipbook */
@media (max-width: 768px) {
  .book-container,
  .flipbook-container {
    perspective: 1000px; /* Maintain good perspective on tablets */
  }

  .flipbook {
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3);
  }

  .page-number {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .book-container,
  .flipbook-container {
    perspective: 800px; /* Better perspective for mobile landscape */
  }

  .flipbook {
    box-shadow: 0 8px 18px rgba(0, 0, 0, 0.3);
  }

  /* Mobile-specific turn.js adjustments */
  .flipbook .page {
    border-radius: 4px;
  }

  .page-header {
    top: 6px;
    right: 10px;
  }

  .page-number {
    font-size: 9px;
  }
}

/* Landscape orientation specific adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .book-container {
    perspective: 1200px;
  }

  .flipbook {
    max-height: 85vh; /* Ensure book fits in landscape view */
  }
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
