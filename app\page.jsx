"use client";

import { useState, useEffect } from "react";
import Background from "@/components/ui/background";
import CursorEffect from "@/components/ui/cursor-effect";
import EnhancedClickEffect from "@/components/ui/enhanced-click-effect";
import FloatingWordsContainer from "@/components/floating-words-container";
import Navbar from "@/components/layout/navbar";
import Footer from "@/components/layout/footer";
import HeroContent from "@/components/home/<USER>";
import useParallax from "@/hooks/use-parallax";
import useMobile from "@/hooks/use-mobile";
import { Cormorant } from "next/font/google";
import PDFViewer from "@/components/portfolio/pdf-viewer";

// Using Cormorant for the elegant serif look
const cormorant = Cormorant({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
});

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  const [portfolioVisible, setPortfolioVisible] = useState(false);
  const { contentPosition } = useParallax(5); // 5px intensity for parallax effect
  const { isMobile, isTablet, isDesktop } = useMobile();

  // Set loaded state and trigger animations
  useEffect(() => {
    // Set loaded state immediately
    setIsLoaded(true);

    // Trigger content fade-in animation after a short delay
    setTimeout(() => {
      setContentVisible(true);
    }, 300);
  }, []);

  return (
    <main className="relative flex min-h-dvh flex-col items-center justify-center overflow-hidden">
      {/* Background */}
      <Background />

      {/* Cursor effect - only on desktop */}
      {isDesktop && <CursorEffect />}

      {/* Enhanced Click effect - only on desktop */}
      <EnhancedClickEffect />

      {/* Navbar */}
      <Navbar
        visible={contentVisible}
        setPortfolioVisible={setPortfolioVisible}
      />

      {/* Portfolio Title */}
      <div
        className="z-10 flex flex-col items-center justify-center absolute top-8 left-0 right-0 transition-all duration-1000 ease-out px-4"
        style={{
          opacity: portfolioVisible ? 1 : 0,
          transform: `translateY(${portfolioVisible ? 0 : 30}px)`,
          pointerEvents: portfolioVisible ? "auto" : "none",
        }}
      >
        <h1
          className={`${cormorant.className} text-center text-2xl md:text-3xl lg:text-4xl font-light tracking-[0.15em] md:tracking-[0.2em] text-white mb-2`}
          style={{
            textShadow: "0 0 5px rgba(255, 255, 255, 0.6)",
          }}
        >
          PORTFOLIO
        </h1>
        <div
          className="w-16 md:w-20 lg:w-24 h-px bg-white opacity-60"
          style={{
            boxShadow: "0 0 3px rgba(255, 255, 255, 0.4)",
          }}
        />
      </div>

      {/* PDF Viewer Container */}
      <div
        className="z-10 flex flex-col items-center justify-center absolute inset-0 mt-24 mb-8 transition-all duration-1000 ease-out px-4"
        style={{
          opacity: portfolioVisible ? 1 : 0,
          transform: `translateY(${portfolioVisible ? 0 : 30}px)`,
          pointerEvents: portfolioVisible ? "auto" : "none",
        }}
      >
        <div className="w-full h-full bg-black/20 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden">
          <PDFViewer />
        </div>
      </div>

      {/* Floating words with magnetic effect */}
      <FloatingWordsContainer
        className="absolute inset-0 overflow-hidden transition-all duration-1000 ease-out"
        style={{
          opacity: portfolioVisible ? 0 : 1,
          transform: `translateY(${portfolioVisible ? -20 : 0}px)`,
          pointerEvents: portfolioVisible ? "none" : "auto",
        }}
      />

      {/* Hero content with parallax effect */}
      <HeroContent
        className="z-10 flex flex-col items-center justify-center absolute inset-0 transition-all duration-1000 ease-out px-4"
        style={{
          opacity: portfolioVisible ? 0 : 1,
          // transform: `translateY(${portfolioVisible ? -20 : 0}px)`,
          pointerEvents: portfolioVisible ? "none" : "auto",
        }}
        contentPosition={contentPosition}
        visible={contentVisible && !portfolioVisible}
      />

      {/* Footer links */}
      <Footer
        className="absolute bottom-4 md:bottom-6 lg:bottom-8 flex items-center gap-4 md:gap-6 z-10 transition-all duration-1000 ease-out px-4"
        style={{
          transform: `translateY(${portfolioVisible ? 20 : 0}px)`,
          opacity: portfolioVisible ? 0 : 1,
          transitionDelay: "200ms",
          pointerEvents: portfolioVisible ? "none" : "auto",
        }}
        visible={contentVisible && !portfolioVisible}
        setPortfolioVisible={setPortfolioVisible}
      />
    </main>
  );
}
