"use client"

import { useState, useEffect, useRef } from "react"
import useMobile from "@/hooks/use-mobile"

export default function FloatingWord({ children, position, animationClass, index, mousePosition, gsapLoaded }) {
  const [isHovered, setIsHovered] = useState(false)
  const [isMagneticEffect, setIsMagneticEffect] = useState(false)
  const wordRef = useRef(null)
  const animationRef = useRef(null)
  const originalPosition = useRef(null)
  const { isMobile, isTablet } = useMobile()

  // Store original position on mount
  useEffect(() => {
    if (wordRef.current) {
      const rect = wordRef.current.getBoundingClientRect()
      originalPosition.current = {
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height,
        centerX: rect.left + rect.width / 2,
        centerY: rect.top + rect.height / 2,
      }
    }
  }, [])

  // Handle magnetic effect and hover state
  useEffect(() => {
    // Skip magnetic effect on mobile and tablet
    if (
      isMobile ||
      isTablet ||
      !gsapLoaded ||
      !window.gsap ||
      !wordRef.current ||
      !mousePosition ||
      !originalPosition.current
    )
      return

    const rect = wordRef.current.getBoundingClientRect()
    const wordCenterX = rect.left + rect.width / 2
    const wordCenterY = rect.top + rect.height / 2

    // Calculate distance between cursor and word
    const dx = mousePosition.x - wordCenterX
    const dy = mousePosition.y - wordCenterY
    const distance = Math.sqrt(dx * dx + dy * dy)

    const magneticRadius = 200 // Radius for magnetic effect
    const hoverRadius = 60 // Smaller radius for visual hover effect

    // Set hover state only when cursor is very close to the word
    setIsHovered(distance < hoverRadius)

    // Set magnetic effect state for the larger radius
    const isNearCursor = distance < magneticRadius
    setIsMagneticEffect(isNearCursor)

    // Apply magnetic effect
    if (isNearCursor) {
      // Calculate attraction strength (stronger when closer)
      const maxPull = 40 // Maximum pixels to pull
      const pull = maxPull * (1 - Math.min(distance / magneticRadius, 1))

      // Calculate angle
      const angle = Math.atan2(dy, dx)

      // Calculate new position with attraction
      const moveX = Math.cos(angle) * pull
      const moveY = Math.sin(angle) * pull

      // Apply the transform with GSAP
      if (animationRef.current) {
        animationRef.current.kill()
      }

      animationRef.current = window.gsap.to(wordRef.current, {
        x: moveX,
        y: moveY,
        duration: 0.6,
        ease: "elastic.out(1, 0.3)",
        overwrite: true,
      })
    } else if (animationRef.current) {
      // Return to original position with a spring effect
      animationRef.current.kill()
      animationRef.current = window.gsap.to(wordRef.current, {
        x: 0,
        y: 0,
        duration: 1,
        ease: "elastic.out(1, 0.3)",
        overwrite: true,
      })
    }
  }, [mousePosition, gsapLoaded, isMobile, isTablet])

  return (
    <span
      ref={wordRef}
      data-word-index={index}
      className={`floating-word absolute text-xl md:text-xl lg:text-xl ${animationClass} hover:scale-125`}
      style={{
        left: position.left,
        top: position.top,
        right: position.right,
        color: isHovered ? "rgba(255, 255, 255, 0.9)" : "#64646d",
        transition: "color 0.3s ease, opacity 0.3s ease",
        opacity: isHovered ? 1 : 0.4,
        transformOrigin: "center center",
      }}
    >
      {children}
    </span>
  )
}
