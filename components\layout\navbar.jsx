"use client";

import { useState } from "react";
import Image from "next/image";
import { Music, Menu } from "lucide-react";
import { Cormorant } from "next/font/google";

// Using Cormorant for the elegant serif look
const cormorant = Cormorant({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
});

export default function Navbar({ visible = true, setPortfolioVisible }) {
  const [isMusicPlaying, setIsMusicPlaying] = useState(false);

  return (
    <div
      className="fixed top-0 left-0 right-0 flex justify-between items-center p-4 md:p-6 z-30 transition-all duration-1000 ease-out"
      style={{
        opacity: visible ? 1 : 0,
        transform: `translateY(${visible ? 0 : -20}px)`,
      }}
    >
      {/* Logo in top left */}
      <button
        onClick={() => setPortfolioVisible && setPortfolioVisible(false)}
        className="relative w-[30px] h-[30px] md:w-[35px] md:h-[35px] lg:w-[40px] lg:h-[40px] opacity-70 hover:opacity-100 transition-opacity duration-300 cursor-pointer"
      >
        <Image
          src="/logo.png"
          alt="The Ivory Bliss Logo"
          fill
          className="object-contain"
        />
      </button>

      {/* Icons in top right */}
      <div className="flex items-center gap-4 md:gap-6">
        <button
          className="text-white opacity-70 hover:opacity-100 transition-opacity"
          onClick={() => setIsMusicPlaying(!isMusicPlaying)}
        >
          <Music
            size={18}
            className={
              isMusicPlaying
                ? "text-white"
                : "text-white/70 md:w-5 md:h-5 lg:w-5 lg:h-5"
            }
          />
        </button>
        <button className="text-white opacity-70 hover:opacity-100 transition-opacity">
          <Menu size={18} className="md:w-5 md:h-5 lg:w-5 lg:h-5" />
        </button>
      </div>
    </div>
  );
}
