"use client"

import { useState, useEffect, useRef } from "react"

export default function ClickEffect() {
  const [particles, setParticles] = useState([])
  const nextIdRef = useRef(0)

  useEffect(() => {
    const handleClick = (e) => {
      const newParticles = []
      // Generate between 10-20 particles per click
      const particleCount = Math.floor(Math.random() * 10) + 10

      for (let i = 0; i < particleCount; i++) {
        // Create random offsets from click position
        const angle = Math.random() * Math.PI * 2 // Random angle
        const distance = Math.random() * 100 + 20 // Random distance (20-120px)

        // Calculate position with some randomness
        const x = e.clientX + Math.cos(angle) * distance
        const y = e.clientY + Math.sin(angle) * distance

        // Random size between 1-3px
        const size = Math.random() * 2 + 1

        // Random duration between 1-3 seconds
        const duration = Math.random() * 2000 + 1000

        // Random delay for staggered appearance
        const delay = Math.random() * 200

        // Random initial opacity
        const opacity = Math.random() * 0.5 + 0.2

        newParticles.push({
          id: nextIdRef.current++,
          x,
          y,
          size,
          opacity,
          duration,
          delay,
          createdAt: Date.now(),
        })
      }

      setParticles((prev) => [...prev, ...newParticles])

      // Clean up old particles
      setTimeout(() => {
        setParticles((prev) =>
          prev.filter((particle) => Date.now() - particle.createdAt < particle.duration + particle.delay + 100),
        )
      }, 3000)
    }

    window.addEventListener("click", handleClick)
    return () => window.removeEventListener("click", handleClick)
  }, [])

  return (
    <div className="pointer-events-none fixed inset-0 z-50 overflow-hidden">
      {particles.map((particle) => {
        const elapsed = Date.now() - particle.createdAt - particle.delay
        const visible = elapsed > 0

        // Calculate current opacity based on lifecycle
        let currentOpacity = 0
        if (visible) {
          const progress = Math.min(elapsed / particle.duration, 1)
          // Fade in quickly, then fade out more slowly
          if (progress < 0.2) {
            currentOpacity = (progress / 0.2) * particle.opacity
          } else {
            currentOpacity = particle.opacity * (1 - (progress - 0.2) / 0.8)
          }
        }

        return visible ? (
          <div
            key={particle.id}
            className="absolute rounded-full bg-white"
            style={{
              left: `${particle.x}px`,
              top: `${particle.y}px`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              opacity: currentOpacity,
              transform: "translate(-50%, -50%)",
            }}
          />
        ) : null
      })}
    </div>
  )
}
