"use client";

import { useState } from "react";
import Link from "next/link";

export default function Footer({
  className,
  style,
  visible = true,
  setPortfolioVisible,
}) {
  const [whatsappHovered, setWhatsappHovered] = useState(false);
  const [instagramHovered, setInstagramHovered] = useState(false);
  const [portfolioHovered, setPortfolioHovered] = useState(false);

  return (
    <div
      className={className}
      style={{
        ...style,
        transitionDelay: "200ms",
      }}
    >
      <Link
        href="https://wa.me/96598078780"
        className="link-item text-[10px] md:text-xs tracking-wider text-white pb-0.5 relative group"
        onMouseEnter={() => setWhatsappHovered(true)}
        onMouseLeave={() => setWhatsappHovered(false)}
      >
        <span className="link-text relative z-10">WHATSAPP</span>
        <span
          className={`absolute bottom-0 h-px bg-white ${
            whatsappHovered
              ? "animate-underline-in"
              : whatsappHovered === false
              ? "animate-underline-out"
              : ""
          }`}
        ></span>
        <span className="link-glow absolute -inset-1 scale-0 rounded-full bg-white/10 transition-all duration-300 group-hover:scale-100 group-active:scale-95 opacity-0 group-hover:opacity-100"></span>
      </Link>
      <Link
        href="https://www.instagram.com/theivorybliss?igsh=MWpqbHVvenlkd3Q0eg=="
        className="link-item text-[10px] md:text-xs tracking-wider text-white pb-0.5 relative group"
        onMouseEnter={() => setInstagramHovered(true)}
        onMouseLeave={() => setInstagramHovered(false)}
      >
        <span className="link-text relative z-10">INSTAGRAM</span>
        <span
          className={`absolute bottom-0 h-px bg-white ${
            instagramHovered
              ? "animate-underline-in"
              : instagramHovered === false
              ? "animate-underline-out"
              : ""
          }`}
        ></span>
        <span className="link-glow absolute -inset-1 scale-0 rounded-full bg-white/10 transition-all duration-300 group-hover:scale-100 group-active:scale-95 opacity-0 group-hover:opacity-100"></span>
      </Link>
      <button
        onClick={() => setPortfolioVisible && setPortfolioVisible(true)}
        className="link-item text-[10px] md:text-xs tracking-wider text-white pb-0.5 relative group cursor-pointer"
        onMouseEnter={() => setPortfolioHovered(true)}
        onMouseLeave={() => setPortfolioHovered(false)}
      >
        <span className="link-text uppercase relative z-10">PORTFOLIO</span>
        <span
          className={`absolute bottom-0 h-px bg-white ${
            portfolioHovered
              ? "animate-underline-in"
              : portfolioHovered === false
              ? "animate-underline-out"
              : ""
          }`}
        ></span>
        <span className="link-glow absolute -inset-1 scale-0 rounded-full bg-white/10 transition-all duration-300 group-hover:scale-100 group-active:scale-95 opacity-0 group-hover:opacity-100"></span>
      </button>
    </div>
  );
}
