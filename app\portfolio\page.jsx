"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { Cormorant } from "next/font/google";
import Background from "@/components/ui/background";
import CursorEffect from "@/components/ui/cursor-effect";
import EnhancedClickEffect from "@/components/ui/enhanced-click-effect";
import PDFViewer from "@/components/portfolio/pdf-viewer";
import useMobile from "@/hooks/use-mobile";

// Using Cormorant for the elegant serif look
const cormorant = Cormorant({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
});

export default function Portfolio() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  const { isMobile, isTablet, isDesktop } = useMobile();

  // Set loaded state and trigger animations
  useEffect(() => {
    // Set loaded state immediately
    setIsLoaded(true);

    // Trigger content fade-in animation after a short delay
    setTimeout(() => {
      setContentVisible(true);
    }, 300);
  }, []);

  return (
    <main className="relative flex min-h-dvh flex-col items-center justify-center overflow-hidden">
      {/* Background */}
      <Background />

      {/* Cursor effect - only on desktop */}
      {isDesktop && <CursorEffect />}

      {/* Enhanced Click effect - only on desktop */}
      <EnhancedClickEffect />

      {/* Back Button */}
      <div
        className="fixed top-0 left-0 p-4 md:p-6 z-30 transition-all duration-1000 ease-out"
        style={{
          opacity: contentVisible ? 1 : 0,
          transform: `translateY(${contentVisible ? 0 : -20}px)`,
        }}
      >
        <Link
          href="/"
          className="flex items-center gap-2 text-white opacity-70 hover:opacity-100 transition-all duration-300 group"
        >
          <ArrowLeft
            size={20}
            className="md:w-6 md:h-6 lg:w-6 lg:h-6 group-hover:transform group-hover:-translate-x-1 transition-transform duration-300"
            style={{
              filter: "drop-shadow(0 0 3px rgba(255, 255, 255, 0.5))",
            }}
          />
          <span
            className={`${cormorant.className} text-sm md:text-base lg:text-lg font-light tracking-[0.1em] hidden sm:inline`}
            style={{
              textShadow: "0 0 3px rgba(255, 255, 255, 0.5)",
            }}
          >
            BACK
          </span>
        </Link>
      </div>

      {/* Portfolio Title */}
      <div
        className="z-10 flex flex-col items-center justify-center absolute top-8 left-0 right-0 transition-all duration-1000 ease-out px-4"
        style={{
          opacity: contentVisible ? 1 : 0,
          transform: `translateY(${contentVisible ? 0 : 30}px)`,
        }}
      >
        <h1
          className={`${cormorant.className} text-center text-2xl md:text-3xl lg:text-4xl font-light tracking-[0.15em] md:tracking-[0.2em] text-white mb-2`}
          style={{
            textShadow: "0 0 5px rgba(255, 255, 255, 0.6)",
          }}
        >
          PORTFOLIO
        </h1>
        <div
          className="w-16 md:w-20 lg:w-24 h-px bg-white opacity-60"
          style={{
            boxShadow: "0 0 3px rgba(255, 255, 255, 0.4)",
          }}
        />
      </div>

      {/* PDF Viewer Container */}
      <div
        className="z-10 flex flex-col items-center justify-center absolute inset-0 mt-24 mb-8 transition-all duration-1000 ease-out px-4"
        style={{
          opacity: contentVisible ? 1 : 0,
          transform: `translateY(${contentVisible ? 0 : 30}px)`,
        }}
      >
        <div className="w-full max-w-5xl h-full bg-black/20 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden">
          <PDFViewer />
        </div>
      </div>
    </main>
  );
}
