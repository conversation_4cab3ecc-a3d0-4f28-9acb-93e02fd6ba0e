"use client";

import { useRef, useState, useEffect } from "react";
import Image from "next/image";
import { Cormorant } from "next/font/google";
import useMobile from "@/hooks/use-mobile";

// Using Cormorant for the elegant serif look
const cormorant = Cormorant({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
});

export default function HeroContent({
  className,
  style,
  contentPosition,
  visible = true,
}) {
  const contentRef = useRef(null);
  const { isMobile, isTablet } = useMobile();
  const [ellipsis, setEllipsis] = useState("");

  // Animate the ellipsis with typewriter effect
  useEffect(() => {
    const ellipsisStates = ["", ".", "..", "..."];
    let currentIndex = 0;

    const interval = setInterval(() => {
      setEllipsis(ellipsisStates[currentIndex]);
      currentIndex = (currentIndex + 1) % ellipsisStates.length;
    }, 500); // Change every 500ms

    return () => clearInterval(interval);
  }, []);

  // Only apply parallax on desktop
  const transformStyle =
    isMobile || isTablet
      ? { transform: `translate(0px, ${visible ? 0 : 30}px)` }
      : {
          transform: `translate(${contentPosition.x}px, ${
            contentPosition.y + (visible ? 0 : 30)
          }px)`,
        };

  return (
    <div
      ref={contentRef}
      className={className}
      style={{
        ...transformStyle,
        ...style,
      }}
    >
      {/* Logo with very subtle glow - positioned above the center, slightly to the left */}
      <div
        className="mb-4 md:mb-6 relative w-[80px] h-[80px] md:w-[100px] md:h-[100px] lg:w-[120px] lg:h-[120px] -mt-[100px] md:-mt-[120px] lg:-mt-[140px] -ml-4 md:-ml-6 lg:-ml-8"
        style={{
          filter: "drop-shadow(0 0 5px rgba(255, 255, 255, 0.6))",
        }}
      >
        <Image
          src="/logo.png"
          alt="The Ivory Bliss Logo"
          fill
          className="object-contain"
        />
      </div>

      {/* Title with very subtle glow - exactly in the center */}
      <h1
        className={`${cormorant.className} text-center text-3xl md:text-4xl lg:text-5xl font-light tracking-[0.15em] md:tracking-[0.2em] text-white`}
        style={{
          textShadow: "0 0 5px rgba(255, 255, 255, 0.6)",
        }}
      >
        THE IVORY BLISS
      </h1>

      {/* Tagline with very subtle glow */}
      <p
        className="mt-2 text-center text-xs md:text-sm tracking-[0.2em] md:tracking-[0.3em] text-white"
        style={{
          textShadow: "0 0 5px rgba(255, 255, 255, 0.6)",
        }}
      >
        DESIGNED TO CELEBRATE
      </p>

      {/* Coming Soon text with prominent styling */}
      <div
        className={`${cormorant.className} mt-8 md:mt-12 text-center text-2xl md:text-3xl lg:text-4xl font-light tracking-[0.15em] md:tracking-[0.2em] text-white`}
        style={{
          textShadow: "0 0 8px rgba(255, 255, 255, 0.8)",
        }}
      >
        COMING SOON
      </div>
    </div>
  );
}
