"use client";

import { useState, useEffect } from "react";
import FloatingWord from "@/components/ui/floating-word";
import Script from "next/script";
import useMobile from "@/hooks/use-mobile";

// Define different positions for different screen sizes
const FLOATING_WORDS = [
  {
    text: "Coordination",
    position: {
      desktop: { left: "25%", top: "22%" },
      tablet: { left: "20%", top: "20%" },
      mobile: { left: "15%", top: "15%" },
    },
    animationClass: "floating-word-1",
  },
  {
    text: "Management",
    position: {
      desktop: { left: "42%", top: "15%" },
      tablet: { left: "40%", top: "12%" },
      mobile: { left: "60%", top: "12%" },
    },
    animationClass: "floating-word-2",
  },
  {
    text: "Events",
    position: {
      desktop: { right: "25%", top: "28%" },
      tablet: { right: "20%", top: "25%" },
      mobile: { right: "10%", top: "25%" },
    },
    animationClass: "floating-word-3",
  },
  {
    text: "Weddings",
    position: {
      desktop: { left: "20%", top: "48%" },
      tablet: { left: "15%", top: "45%" },
      mobile: { left: "20%", top: "40%" },
    },
    animationClass: "floating-word-4",
  },
  {
    text: "Design",
    position: {
      desktop: { right: "22%", top: "45%" },
      tablet: { right: "18%", top: "42%" },
      mobile: { right: "25%", top: "45%" },
    },
    animationClass: "floating-word-5",
  },
  {
    text: "Execution",
    position: {
      desktop: { left: "30%", top: "65%" },
      tablet: { left: "25%", top: "62%" },
      mobile: { left: "10%", top: "60%" },
    },
    animationClass: "floating-word-6",
  },
  {
    text: "Transform",
    position: {
      desktop: { left: "45%", top: "72%" },
      tablet: { left: "42%", top: "70%" },
      mobile: { left: "50%", top: "75%" },
    },
    animationClass: "floating-word-7",
  },
  {
    text: "Consultation",
    position: {
      desktop: { right: "22%", top: "65%" },
      tablet: { right: "18%", top: "62%" },
      mobile: { right: "15%", top: "65%" },
    },
    animationClass: "floating-word-8",
  },
  {
    text: "Interior",
    position: {
      desktop: { left: "18%", top: "35%" },
      tablet: { left: "15%", top: "32%" },
      mobile: { left: "12%", top: "30%" },
    },
    animationClass: "floating-word-9",
  },
  {
    text: "Renovate",
    position: {
      desktop: { right: "28%", top: "18%" },
      tablet: { right: "25%", top: "15%" },
      mobile: { right: "20%", top: "18%" },
    },
    animationClass: "floating-word-10",
  },
  {
    text: "Remold",
    position: {
      desktop: { left: "38%", top: "58%" },
      tablet: { left: "35%", top: "55%" },
      mobile: { left: "30%", top: "52%" },
    },
    animationClass: "floating-word-11",
  },
  {
    text: "Reupholster",
    position: {
      desktop: { right: "15%", top: "32%" },
      tablet: { right: "12%", top: "30%" },
      mobile: { right: "8%", top: "35%" },
    },
    animationClass: "floating-word-12",
  },
  {
    text: "Visualize",
    position: {
      desktop: { left: "52%", top: "42%" },
      tablet: { left: "48%", top: "40%" },
      mobile: { left: "45%", top: "38%" },
    },
    animationClass: "floating-word-13",
  },
];

export default function FloatingWordsContainer({ className, style }) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [gsapLoaded, setGsapLoaded] = useState(false);
  const { isMobile, isTablet, isDesktop } = useMobile();

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, []);

  // Handle GSAP loading
  const handleGsapLoad = () => {
    setGsapLoaded(true);
  };

  // Get the appropriate position based on device type
  const getPosition = (word) => {
    if (isMobile) return word.position.mobile;
    if (isTablet) return word.position.tablet;
    return word.position.desktop;
  };

  return (
    <div className={className} style={style}>
      {/* Load GSAP from CDN */}
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"
        onLoad={handleGsapLoad}
        strategy="afterInteractive"
      />

      {FLOATING_WORDS.map((word, index) => (
        <FloatingWord
          key={index}
          index={index}
          position={getPosition(word)}
          animationClass={word.animationClass}
          mousePosition={mousePosition}
          gsapLoaded={gsapLoaded && isDesktop} // Only enable GSAP effects on desktop
        >
          {word.text}
        </FloatingWord>
      ))}
    </div>
  );
}
