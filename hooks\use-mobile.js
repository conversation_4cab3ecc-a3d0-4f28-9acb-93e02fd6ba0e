"use client"

import { useState, useEffect } from "react"

export default function useMobile() {
  const [isMobile, setIsMobile] = useState(false)
  const [isTablet, setIsTablet] = useState(false)
  const [isDesktop, setIsDesktop] = useState(true)

  useEffect(() => {
    // Function to check device type based on screen width
    const checkDevice = () => {
      const width = window.innerWidth
      setIsMobile(width < 640) // Mobile: < 640px
      setIsTablet(width >= 640 && width < 1024) // Tablet: 640px - 1023px
      setIsDesktop(width >= 1024) // Desktop: >= 1024px
    }

    // Initial check
    checkDevice()

    // Add event listener for window resize
    window.addEventListener("resize", checkDevice)

    // Cleanup
    return () => window.removeEventListener("resize", checkDevice)
  }, [])

  return { isMobile, isTablet, isDesktop }
}
