"use client"

import { useState, useEffect } from "react"

export default function CustomCursor() {
  const [position, setPosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const updatePosition = (e) => {
      setPosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener("mousemove", updatePosition)
    return () => window.removeEventListener("mousemove", updatePosition)
  }, [])

  return (
    <div
      className="fixed w-[150px] h-[150px] rounded-full pointer-events-none z-50 mix-blend-soft-light"
      style={{
        background: "radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%)",
        left: `${position.x - 75}px`,
        top: `${position.y - 75}px`,
        transform: "translate(-50%, -50%)",
      }}
    />
  )
}
