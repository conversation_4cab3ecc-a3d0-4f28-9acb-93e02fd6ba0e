"use client"

import { useState, useEffect, useRef } from "react"

export default function CursorEffect() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const cursorRef = useRef({ x: 0, y: 0 })
  const glowRef = useRef(null)

  // Smooth follow animation for cursor glow
  useEffect(() => {
    let animationFrameId

    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener("mousemove", handleMouseMove)

    const animateCursor = () => {
      if (!glowRef.current) return

      // Smooth easing animation
      const easing = 0.1

      // Calculate distance between current position and target
      const dx = mousePosition.x - cursorRef.current.x
      const dy = mousePosition.y - cursorRef.current.y

      // Apply easing
      cursorRef.current.x += dx * easing
      cursorRef.current.y += dy * easing

      // Update cursor glow position
      glowRef.current.style.left = `${cursorRef.current.x}px`
      glowRef.current.style.top = `${cursorRef.current.y}px`

      animationFrameId = requestAnimationFrame(animateCursor)
    }

    animationFrameId = requestAnimationFrame(animateCursor)

    return () => {
      window.removeEventListener("mousemove", handleMouseMove)
      cancelAnimationFrame(animationFrameId)
    }
  }, [mousePosition])

  return (
    <div
      ref={glowRef}
      className="fixed w-[150px] h-[150px] rounded-full pointer-events-none z-20 mix-blend-soft-light"
      style={{
        background:
          "radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 40%, rgba(255,255,255,0) 60%)",
        transform: "translate(-50%, -50%)",
      }}
    />
  )
}
