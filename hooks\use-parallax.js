"use client"

import { useState, useEffect } from "react"
import useMobile from "./use-mobile"

export default function useParallax(intensity = 5) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 })
  const [contentPosition, setContentPosition] = useState({ x: 0, y: 0 })
  const { isMobile, isTablet } = useMobile()

  // Track window size
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    // Set initial size
    handleResize()

    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  // Track mouse position
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => {
      window.removeEventListener("mousemove", handleMouseMove)
    }
  }, [])

  // Calculate content position based on mouse position
  useEffect(() => {
    // Skip parallax effect on mobile and tablet
    if (isMobile || isTablet || windowSize.width === 0 || windowSize.height === 0) {
      setContentPosition({ x: 0, y: 0 })
      return
    }

    // Calculate mouse position relative to center of screen
    const centerX = windowSize.width / 2
    const centerY = windowSize.height / 2

    // Calculate offset from center (normalized to -1 to 1)
    const offsetX = (mousePosition.x - centerX) / centerX
    const offsetY = (mousePosition.y - centerY) / centerY

    // Apply movement intensity
    setContentPosition({
      x: -offsetX * intensity,
      y: -offsetY * intensity,
    })
  }, [mousePosition, windowSize, intensity, isMobile, isTablet])

  return { mousePosition, contentPosition }
}
