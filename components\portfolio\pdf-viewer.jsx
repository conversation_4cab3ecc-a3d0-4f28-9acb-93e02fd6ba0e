"use client";

import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Cormorant } from "next/font/google";
import useMobile from "@/hooks/use-mobile";

// Using Cormorant for the elegant serif look
const cormorant = Cormorant({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
});

export default function PDFViewer() {
  const [portfolioPages, setPortfolioPages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageDimensions, setPageDimensions] = useState({
    width: 800,
    height: 600,
  });
  const [dualPageDimensions, setDualPageDimensions] = useState({
    width: 800,
    height: 600,
  });
  const [isFlipping, setIsFlipping] = useState(false);
  const [flipDirection, setFlipDirection] = useState("next"); // 'next' or 'prev'
  const [nextPageIndex, setNextPageIndex] = useState(null);

  const { isMobile } = useMobile();

  // Helper function to determine if current page should be single or dual
  const isCurrentPageSingle = (pageIndex) => {
    if (isMobile) return true;
    // First page (index 0) and last page are single
    return pageIndex === 0 || pageIndex === portfolioPages.length - 1;
  };

  // Helper function to determine if we're in dual page mode
  const isDualPageMode = () => {
    return !isMobile && !isCurrentPageSingle(currentPage);
  };

  // Load portfolio images
  useEffect(() => {
    const loadPortfolioImages = () => {
      try {
        setIsLoading(true);
        setError(null);

        // Generate array of image paths (68 pages from 0001 to 0068)
        const pages = [];
        for (let i = 1; i <= 68; i++) {
          const pageNumber = i.toString().padStart(4, "0");
          pages.push({
            pageNumber: i,
            imageUrl: `/tib_portfolio_images/tib_portfolio_page-${pageNumber}.jpg`,
          });
        }

        setPortfolioPages(pages);
        setIsLoading(false);
        // console.log(`Successfully loaded ${pages.length} portfolio pages`);
      } catch (err) {
        console.error("Error loading portfolio images:", err);
        setError("Failed to load portfolio images");
        setIsLoading(false);
      }
    };

    loadPortfolioImages();
  }, []);

  // Dynamic sizing based on screen dimensions - both single and dual page views
  useEffect(() => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Calculate available space (leaving room for controls and margins)
    const availableWidth = windowWidth - 80; // 40px margin on each side
    const availableHeight = windowHeight - 160; // Space for header, controls, margins

    // Aspect ratio for landscape pages (1.414 = √2)
    const pageAspectRatio = 1.414;

    // SINGLE PAGE DIMENSIONS (for cover and back cover)
    let singlePageWidth, singlePageHeight;

    // Calculate single page dimensions based on available space
    if (availableWidth / pageAspectRatio <= availableHeight) {
      // Width is the limiting factor
      singlePageWidth = availableWidth;
      singlePageHeight = singlePageWidth / pageAspectRatio;
    } else {
      // Height is the limiting factor
      singlePageHeight = availableHeight;
      singlePageWidth = singlePageHeight * pageAspectRatio;
    }

    // Apply constraints for single page
    singlePageWidth = Math.max(singlePageWidth, 300);
    singlePageHeight = Math.max(singlePageHeight, 212);
    singlePageWidth = Math.min(singlePageWidth, 1200);
    singlePageHeight = Math.min(singlePageHeight, 848);

    // DUAL PAGE DIMENSIONS (for interior pages)
    let dualPageWidth, dualPageHeight;

    if (!isMobile) {
      // For dual pages, we need to fit two pages side by side
      // Total width will be 2 * pageWidth, so each page is availableWidth / 2
      const dualPageSingleWidth = availableWidth / 2;
      const dualPageSingleHeight = dualPageSingleWidth / pageAspectRatio;

      // Check if this height fits in available space
      if (dualPageSingleHeight <= availableHeight) {
        dualPageWidth = availableWidth;
        dualPageHeight = dualPageSingleHeight;
      } else {
        // Height is limiting, so calculate based on height
        dualPageHeight = availableHeight;
        const singlePageWidthFromHeight = dualPageHeight * pageAspectRatio;
        dualPageWidth = singlePageWidthFromHeight * 2;
      }

      // Apply constraints for dual page
      dualPageWidth = Math.max(dualPageWidth, 600); // Minimum for two pages
      dualPageHeight = Math.max(dualPageHeight, 212);
      dualPageWidth = Math.min(dualPageWidth, 1600); // Maximum for two pages
      dualPageHeight = Math.min(dualPageHeight, 565);
    } else {
      // On mobile, dual page dimensions same as single
      dualPageWidth = singlePageWidth;
      dualPageHeight = singlePageHeight;
    }

    setPageDimensions({
      width: Math.round(singlePageWidth),
      height: Math.round(singlePageHeight),
    });

    setDualPageDimensions({
      width: Math.round(dualPageWidth),
      height: Math.round(dualPageHeight),
    });

    // console.log("Single page dimensions:", {
    //   width: Math.round(singlePageWidth),
    //   height: Math.round(singlePageHeight),
    // });
    // console.log("Dual page dimensions:", {
    //   width: Math.round(dualPageWidth),
    //   height: Math.round(dualPageHeight),
    // });
  }, [isMobile]);

  // Custom navigation logic for dual-page book experience
  const getMaxPageIndex = () => {
    if (isMobile) return portfolioPages.length - 1;

    // For desktop dual-page mode, we need to handle page progression differently
    // First page (0) is single, then we show pairs: (1,2), (3,4), (5,6), etc.
    // Last page is single if it's odd-numbered
    return portfolioPages.length - 1;
  };

  const getNextPageIndex = (currentIndex) => {
    if (isMobile) {
      // Mobile: simple increment
      return Math.min(currentIndex + 1, portfolioPages.length - 1);
    }

    // Desktop logic
    if (currentIndex === 0) {
      // From cover (0) to first spread (1)
      return 1;
    } else if (currentIndex === portfolioPages.length - 1) {
      // Already at last page
      return currentIndex;
    } else if (isCurrentPageSingle(currentIndex)) {
      // From single page, go to next page
      return Math.min(currentIndex + 1, portfolioPages.length - 1);
    } else {
      // From dual page, skip to next odd page (start of next spread)
      const nextIndex = currentIndex + 2;
      return Math.min(nextIndex, portfolioPages.length - 1);
    }
  };

  const getPrevPageIndex = (currentIndex) => {
    if (isMobile) {
      // Mobile: simple decrement
      return Math.max(currentIndex - 1, 0);
    }

    // Desktop logic
    if (currentIndex === 0) {
      // Already at cover
      return 0;
    } else if (currentIndex === 1) {
      // From first spread back to cover
      return 0;
    } else if (isCurrentPageSingle(currentIndex)) {
      // From single page (last page), go to previous spread
      return Math.max(currentIndex - 2, 1);
    } else {
      // From dual page, go to previous spread
      const prevIndex = currentIndex - 2;
      return Math.max(prevIndex, 0);
    }
  };

  // Custom navigation functions with flip animations
  const nextPage = () => {
    const nextIndex = getNextPageIndex(currentPage);
    if (nextIndex !== currentPage && !isFlipping) {
      setIsFlipping(true);
      setFlipDirection("next");
      setNextPageIndex(nextIndex);

      // Start the flip animation
      setTimeout(() => {
        setCurrentPage(nextIndex);
        setTimeout(() => {
          setIsFlipping(false);
          setNextPageIndex(null);
        }, 400); // Half of the animation duration
      }, 400); // Half of the animation duration

      console.log(`Flipping to page ${nextIndex + 1}`);
    }
  };

  const prevPage = () => {
    const prevIndex = getPrevPageIndex(currentPage);
    if (prevIndex !== currentPage && !isFlipping) {
      setIsFlipping(true);
      setFlipDirection("prev");
      setNextPageIndex(prevIndex);

      // Start the flip animation
      setTimeout(() => {
        setCurrentPage(prevIndex);
        setTimeout(() => {
          setIsFlipping(false);
          setNextPageIndex(null);
        }, 400); // Half of the animation duration
      }, 400); // Half of the animation duration

      console.log(`Flipping to page ${prevIndex + 1}`);
    }
  };

  // Check if navigation is possible
  const canGoNext = () => {
    return getNextPageIndex(currentPage) !== currentPage;
  };

  const canGoPrev = () => {
    return getPrevPageIndex(currentPage) !== currentPage;
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event) => {
      switch (event.key) {
        case "ArrowLeft":
          event.preventDefault();
          if (canGoPrev()) {
            prevPage();
          }
          break;
        case "ArrowRight":
          event.preventDefault();
          if (canGoNext()) {
            nextPage();
          }
          break;
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [currentPage, isMobile, portfolioPages.length]);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center relative">
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p
              className={`${cormorant.className} text-white/70 text-lg font-light tracking-[0.1em]`}
              style={{
                textShadow: "0 0 3px rgba(255, 255, 255, 0.3)",
              }}
            >
              Loading Portfolio...
            </p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="text-center p-8">
            <p
              className={`${cormorant.className} text-white/70 text-lg font-light tracking-[0.1em]`}
              style={{
                textShadow: "0 0 3px rgba(255, 255, 255, 0.3)",
              }}
            >
              {error}
            </p>
            <p className="text-white/50 text-sm mt-2 mb-4">
              Please check if the images exist and are accessible.
            </p>
            <button
              onClick={() => {
                setError(null);
                setIsLoading(true);
                window.location.reload();
              }}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-all duration-1000 border border-white/20"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Turn.js Flipbook */}
      {!isLoading && !error && portfolioPages.length > 0 && (
        <div className="flex-1 flex items-center justify-center overflow-hidden w-full relative">
          {/* Left Navigation Button */}
          <button
            onClick={prevPage}
            disabled={!canGoPrev()}
            className="absolute -left-1 z-[999] disabled:opacity-50 disabled:cursor-not-allowed text-white md:hover:text-white/50 transition-all duration-800"
            aria-label="Previous page"
          >
            <ChevronLeft className="w-16 h-16" />
          </button>

          {/* Custom Book Layout with Flip Animation */}
          <div
            className="book-container"
            style={{
              width: `${
                isDualPageMode()
                  ? dualPageDimensions.width
                  : pageDimensions.width
              }px`,
              height: `${
                isDualPageMode()
                  ? dualPageDimensions.height
                  : pageDimensions.height
              }px`,
              margin: "0 auto",
              position: "relative",
              perspective: "2000px",
              transformStyle: "preserve-3d",
              transition: "width 0.8s ease-in-out, height 0.8s ease-in-out",
            }}
          >
            {/* Current Page(s) */}
            <div
              className="current-pages"
              style={{
                width: "100%",
                height: "100%",
                position: "absolute",
                top: 0,
                left: 0,
                transformStyle: "preserve-3d",
                transform: isFlipping
                  ? flipDirection === "next"
                    ? "rotateY(-90deg)"
                    : "rotateY(90deg)"
                  : "rotateY(0deg)",
                transition: isFlipping ? "transform 0.8s ease-in-out" : "none",
                backfaceVisibility: "hidden",
              }}
            >
              {/* Single Page Mode (Cover, Back Cover, Mobile) */}
              {(isMobile || isCurrentPageSingle(currentPage)) && (
                <div
                  className="single-page-container"
                  style={{
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: "8px",
                    overflow: "hidden",
                    boxShadow: "0 15px 40px rgba(0,0,0,0.4)",
                    background: "linear-gradient(145deg, #f8f8f8, #e8e8e8)",
                  }}
                >
                  <img
                    src={portfolioPages[currentPage]?.imageUrl}
                    alt={`Portfolio page ${currentPage + 1}`}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "contain",
                      display: "block",
                    }}
                    onError={(e) => {
                      console.error(
                        `Failed to load image: ${portfolioPages[currentPage]?.imageUrl}`
                      );
                      e.target.style.display = "none";
                    }}
                  />
                </div>
              )}

              {/* Dual Page Mode (Interior Pages on Desktop) */}
              {!isMobile && !isCurrentPageSingle(currentPage) && (
                <div
                  className="dual-page-container"
                  style={{
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    gap: "2px",
                    borderRadius: "8px",
                    overflow: "hidden",
                    boxShadow: "0 15px 40px rgba(0,0,0,0.4)",
                    background: "linear-gradient(145deg, #f8f8f8, #e8e8e8)",
                  }}
                >
                  {/* Left Page */}
                  <div
                    className="page-left"
                    style={{
                      width: "50%",
                      height: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      borderRadius: "8px 0 0 8px",
                      overflow: "hidden",
                      position: "relative",
                    }}
                  >
                    <div
                      style={{
                        position: "absolute",
                        top: 0,
                        right: 0,
                        width: "1px",
                        height: "100%",
                        background:
                          "linear-gradient(to bottom, transparent, rgba(0,0,0,0.1), transparent)",
                      }}
                    />
                    <img
                      src={portfolioPages[currentPage]?.imageUrl}
                      alt={`Portfolio page ${currentPage + 1}`}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "contain",
                        display: "block",
                      }}
                      onError={(e) => {
                        console.error(
                          `Failed to load image: ${portfolioPages[currentPage]?.imageUrl}`
                        );
                        e.target.style.display = "none";
                      }}
                    />
                  </div>

                  {/* Right Page */}
                  <div
                    className="page-right"
                    style={{
                      width: "50%",
                      height: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      borderRadius: "0 8px 8px 0",
                      overflow: "hidden",
                    }}
                  >
                    {portfolioPages[currentPage + 1] && (
                      <img
                        src={portfolioPages[currentPage + 1]?.imageUrl}
                        alt={`Portfolio page ${currentPage + 2}`}
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "contain",
                          display: "block",
                        }}
                        onError={(e) => {
                          console.error(
                            `Failed to load image: ${
                              portfolioPages[currentPage + 1]?.imageUrl
                            }`
                          );
                          e.target.style.display = "none";
                        }}
                      />
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Next Page(s) - Visible during flip animation */}
            {isFlipping && nextPageIndex !== null && (
              <div
                className="next-pages"
                style={{
                  width: "100%",
                  height: "100%",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  transformStyle: "preserve-3d",
                  transform:
                    flipDirection === "next"
                      ? "rotateY(90deg)"
                      : "rotateY(-90deg)",
                  transition: "transform 0.8s ease-in-out",
                  backfaceVisibility: "hidden",
                }}
              >
                {/* Single Page Mode for Next Page */}
                {(isMobile || isCurrentPageSingle(nextPageIndex)) && (
                  <div
                    className="single-page-container"
                    style={{
                      width: "100%",
                      height: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      borderRadius: "8px",
                      overflow: "hidden",
                      boxShadow: "0 15px 40px rgba(0,0,0,0.4)",
                      background: "linear-gradient(145deg, #f8f8f8, #e8e8e8)",
                    }}
                  >
                    <img
                      src={portfolioPages[nextPageIndex]?.imageUrl}
                      alt={`Portfolio page ${nextPageIndex + 1}`}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "contain",
                        display: "block",
                      }}
                      onError={(e) => {
                        console.error(
                          `Failed to load image: ${portfolioPages[nextPageIndex]?.imageUrl}`
                        );
                        e.target.style.display = "none";
                      }}
                    />
                  </div>
                )}

                {/* Dual Page Mode for Next Pages */}
                {!isMobile && !isCurrentPageSingle(nextPageIndex) && (
                  <div
                    className="dual-page-container"
                    style={{
                      width: "100%",
                      height: "100%",
                      display: "flex",
                      gap: "2px",
                      borderRadius: "8px",
                      overflow: "hidden",
                      boxShadow: "0 15px 40px rgba(0,0,0,0.4)",
                      background: "linear-gradient(145deg, #f8f8f8, #e8e8e8)",
                    }}
                  >
                    {/* Left Page */}
                    <div
                      className="page-left"
                      style={{
                        width: "50%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        borderRadius: "8px 0 0 8px",
                        overflow: "hidden",
                        position: "relative",
                      }}
                    >
                      <div
                        style={{
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "1px",
                          height: "100%",
                          background:
                            "linear-gradient(to bottom, transparent, rgba(0,0,0,0.1), transparent)",
                        }}
                      />
                      <img
                        src={portfolioPages[nextPageIndex]?.imageUrl}
                        alt={`Portfolio page ${nextPageIndex + 1}`}
                        style={{
                          width: "100%",
                          height: "100%",
                          objectFit: "contain",
                          display: "block",
                        }}
                        onError={(e) => {
                          console.error(
                            `Failed to load image: ${portfolioPages[nextPageIndex]?.imageUrl}`
                          );
                          e.target.style.display = "none";
                        }}
                      />
                    </div>

                    {/* Right Page */}
                    <div
                      className="page-right"
                      style={{
                        width: "50%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        borderRadius: "0 8px 8px 0",
                        overflow: "hidden",
                      }}
                    >
                      {portfolioPages[nextPageIndex + 1] && (
                        <img
                          src={portfolioPages[nextPageIndex + 1]?.imageUrl}
                          alt={`Portfolio page ${nextPageIndex + 2}`}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "contain",
                            display: "block",
                          }}
                          onError={(e) => {
                            console.error(
                              `Failed to load image: ${
                                portfolioPages[nextPageIndex + 1]?.imageUrl
                              }`
                            );
                            e.target.style.display = "none";
                          }}
                        />
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Right Navigation Button */}
          <button
            onClick={nextPage}
            disabled={!canGoNext()}
            className="absolute -right-1 z-[999] disabled:opacity-50 disabled:cursor-not-allowed text-white md:hover:text-white/50 transition-all duration-800"
            aria-label="Next page"
          >
            <ChevronRight className="w-16 h-16" />
          </button>
        </div>
      )}

      {/* Page Counter */}
      {!isLoading && !error && portfolioPages.length > 0 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-4 bg-black/30 backdrop-blur-sm rounded-full px-6 py-3 border border-white/10">
          <div className="flex items-center gap-2">
            <span
              className={`${cormorant.className} text-white text-sm font-light tracking-[0.1em]`}
              style={{
                textShadow: "0 0 3px rgba(255, 255, 255, 0.5)",
              }}
            >
              {!isMobile &&
              !isCurrentPageSingle(currentPage) &&
              portfolioPages[currentPage + 1]
                ? `${currentPage + 1}-${currentPage + 2} / ${
                    portfolioPages.length
                  }`
                : `${currentPage + 1} / ${portfolioPages.length}`}
            </span>
          </div>
        </div>
      )}

      {/* Keyboard Navigation Hint */}
      {!isLoading && !error && !isMobile && portfolioPages.length > 0 && (
        <div className="absolute top-4 right-4 text-white/50 text-xs">
          <p className={`${cormorant.className} font-light tracking-[0.1em]`}>
            Use ← → keys to navigate pages
          </p>
        </div>
      )}
    </div>
  );
}
