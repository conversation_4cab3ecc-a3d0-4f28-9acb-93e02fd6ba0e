"use client";

import { useState, useEffect, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Cormorant } from "next/font/google";
import useMobile from "@/hooks/use-mobile";

// Using Cormorant for the elegant serif look
const cormorant = Cormorant({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600"],
});

export default function PDFViewer() {
  const [portfolioPages, setPortfolioPages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageDimensions, setPageDimensions] = useState({
    width: 800,
    height: 600,
  });
  const [dualPageDimensions, setDualPageDimensions] = useState({
    width: 800,
    height: 600,
  });
  const flipbookRef = useRef(null);
  const { isMobile } = useMobile();

  // Helper function to determine if current page should be single or dual
  const isCurrentPageSingle = (pageIndex) => {
    if (isMobile) return true;
    // First page (index 0) and last page are single
    return pageIndex === 0 || pageIndex === portfolioPages.length - 1;
  };

  // Helper function to determine if we're in dual page mode
  const isDualPageMode = () => {
    return !isMobile && !isCurrentPageSingle(currentPage);
  };

  // Load portfolio images
  useEffect(() => {
    const loadPortfolioImages = () => {
      try {
        setIsLoading(true);
        setError(null);

        // Generate array of image paths (68 pages from 0001 to 0068)
        const pages = [];
        for (let i = 1; i <= 68; i++) {
          const pageNumber = i.toString().padStart(4, "0");
          pages.push({
            pageNumber: i,
            imageUrl: `/tib_portfolio_images/tib_portfolio_page-${pageNumber}.jpg`,
          });
        }

        setPortfolioPages(pages);
        setIsLoading(false);
        // console.log(`Successfully loaded ${pages.length} portfolio pages`);
      } catch (err) {
        console.error("Error loading portfolio images:", err);
        setError("Failed to load portfolio images");
        setIsLoading(false);
      }
    };

    loadPortfolioImages();
  }, []);

  // Dynamic sizing based on screen dimensions - both single and dual page views
  useEffect(() => {
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // Calculate available space (leaving room for controls and margins)
    const availableWidth = windowWidth - 80; // 40px margin on each side
    const availableHeight = windowHeight - 160; // Space for header, controls, margins

    // Aspect ratio for landscape pages (1.414 = √2)
    const pageAspectRatio = 1.414;

    // SINGLE PAGE DIMENSIONS (for cover and back cover)
    let singlePageWidth, singlePageHeight;

    // Calculate single page dimensions based on available space
    if (availableWidth / pageAspectRatio <= availableHeight) {
      // Width is the limiting factor
      singlePageWidth = availableWidth;
      singlePageHeight = singlePageWidth / pageAspectRatio;
    } else {
      // Height is the limiting factor
      singlePageHeight = availableHeight;
      singlePageWidth = singlePageHeight * pageAspectRatio;
    }

    // Apply constraints for single page
    singlePageWidth = Math.max(singlePageWidth, 300);
    singlePageHeight = Math.max(singlePageHeight, 212);
    singlePageWidth = Math.min(singlePageWidth, 1200);
    singlePageHeight = Math.min(singlePageHeight, 848);

    // DUAL PAGE DIMENSIONS (for interior pages)
    let dualPageWidth, dualPageHeight;

    if (!isMobile) {
      // For dual pages, we need to fit two pages side by side
      // Total width will be 2 * pageWidth, so each page is availableWidth / 2
      const dualPageSingleWidth = availableWidth / 2;
      const dualPageSingleHeight = dualPageSingleWidth / pageAspectRatio;

      // Check if this height fits in available space
      if (dualPageSingleHeight <= availableHeight) {
        dualPageWidth = availableWidth;
        dualPageHeight = dualPageSingleHeight;
      } else {
        // Height is limiting, so calculate based on height
        dualPageHeight = availableHeight;
        const singlePageWidthFromHeight = dualPageHeight * pageAspectRatio;
        dualPageWidth = singlePageWidthFromHeight * 2;
      }

      // Apply constraints for dual page
      dualPageWidth = Math.max(dualPageWidth, 600); // Minimum for two pages
      dualPageHeight = Math.max(dualPageHeight, 212);
      dualPageWidth = Math.min(dualPageWidth, 1600); // Maximum for two pages
      dualPageHeight = Math.min(dualPageHeight, 565);
    } else {
      // On mobile, dual page dimensions same as single
      dualPageWidth = singlePageWidth;
      dualPageHeight = singlePageHeight;
    }

    setPageDimensions({
      width: Math.round(singlePageWidth),
      height: Math.round(singlePageHeight),
    });

    setDualPageDimensions({
      width: Math.round(dualPageWidth),
      height: Math.round(dualPageHeight),
    });

    // console.log("Single page dimensions:", {
    //   width: Math.round(singlePageWidth),
    //   height: Math.round(singlePageHeight),
    // });
    // console.log("Dual page dimensions:", {
    //   width: Math.round(dualPageWidth),
    //   height: Math.round(dualPageHeight),
    // });
  }, [isMobile]);

  // Initialize turn.js when portfolio pages are loaded
  useEffect(() => {
    if (portfolioPages.length > 0 && flipbookRef.current && !isLoading) {
      // Dynamically import jQuery and turn.js
      const initializeTurnJS = async () => {
        try {
          // Import jQuery
          const $ = (await import("jquery")).default;

          // Make jQuery available globally for turn.js
          if (typeof window !== "undefined") {
            window.$ = window.jQuery = $;
          }

          // Import turn.js
          await import("@ksedline/turnjs");

          // Initialize the flipbook
          const $flipbook = $(flipbookRef.current);

          // Destroy existing instance if it exists
          if ($flipbook.data("turn")) {
            $flipbook.turn("destroy");
          }

          // Determine current display mode and dimensions
          const currentIsSingle = isCurrentPageSingle(currentPage);
          const currentDimensions = currentIsSingle
            ? pageDimensions
            : dualPageDimensions;
          const displayMode = isMobile
            ? "single"
            : currentIsSingle
            ? "single"
            : "double";

          // Initialize turn.js with dynamic configuration
          $flipbook.turn({
            width: currentDimensions.width,
            height: currentDimensions.height,
            autoCenter: true,
            display: displayMode,
            acceleration: true,
            gradients: true,
            elevation: 50,
            duration: 1000,
            pages: portfolioPages.length,
            page: currentPage + 1, // Convert from 0-based to 1-based
            when: {
              turning: function (_, page) {
                const newPageIndex = Number(page) - 1;
                // console.log(`Turning to page ${page} (index: ${newPageIndex})`);

                // Check if we need to change display mode
                const newIsSingle = isCurrentPageSingle(newPageIndex);
                const currentIsSingle = isCurrentPageSingle(currentPage);

                if (newIsSingle !== currentIsSingle && !isMobile) {
                  // We need to change display mode - this will trigger a re-initialization
                  const newDimensions = newIsSingle
                    ? pageDimensions
                    : dualPageDimensions;
                  const newDisplayMode = newIsSingle ? "single" : "double";

                  // Update dimensions and display mode
                  setTimeout(() => {
                    $flipbook.turn(
                      "size",
                      newDimensions.width,
                      newDimensions.height
                    );
                    $flipbook.turn("display", newDisplayMode);
                  }, 100);
                }

                // Update current page state
                setCurrentPage(newPageIndex);
              },
              turned: function (_, page) {
                // console.log(`Turned to page ${page}`);
              },
            },
          });
        } catch (error) {
          console.error("Failed to initialize turn.js:", error);
        }
      };

      initializeTurnJS();
    }
  }, [
    portfolioPages.length,
    pageDimensions,
    dualPageDimensions,
    isLoading,
    isMobile,
  ]);

  // Handle dynamic reconfiguration when switching between single/dual modes
  useEffect(() => {
    console.log(pageDimensions, dualPageDimensions);
    if (
      portfolioPages.length > 0 &&
      flipbookRef.current &&
      !isLoading &&
      window.$
    ) {
      const $ = window.$;
      const $flipbook = $(flipbookRef.current);

      if ($flipbook.data("turn")) {
        const currentIsSingle = isCurrentPageSingle(currentPage);
        const currentDimensions = currentIsSingle
          ? pageDimensions
          : dualPageDimensions;
        const displayMode = isMobile
          ? "single"
          : currentIsSingle
          ? "single"
          : "double";

        // Update turn.js configuration
        try {
          $flipbook.turn(
            "size",
            currentDimensions.width,
            currentDimensions.height
          );
          $flipbook.turn("display", displayMode);
          // console.log(
          // `Updated flipbook: ${displayMode} mode, ${currentDimensions.width}x${currentDimensions.height}`
          // );
        } catch (error) {
          console.error("Error updating flipbook configuration:", error);
        }
      }
    }
  }, [
    currentPage,
    pageDimensions,
    dualPageDimensions,
    isMobile,
    portfolioPages.length,
    isLoading,
  ]);

  // Navigation functions for turn.js
  const nextPage = () => {
    if (flipbookRef.current && window.$) {
      const $ = window.$;
      const $flipbook = $(flipbookRef.current);
      // console.log("nextPage - Turn.js data:", $flipbook.data("turn"));

      try {
        // Try to call turn methods directly
        const currentTurnPage = $flipbook.turn("page");
        const totalPages = $flipbook.turn("pages");
        // console.log(`Current: ${currentTurnPage}, Total: ${totalPages}`);

        if (currentTurnPage < totalPages) {
          $flipbook.turn("next");
          // console.log("Called turn next");
        }
      } catch (error) {
        console.error("Error in nextPage:", error);
      }
    }
  };

  const prevPage = () => {
    if (flipbookRef.current && window.$) {
      const $ = window.$;
      const $flipbook = $(flipbookRef.current);
      // console.log("prevPage - Turn.js data:", $flipbook.data("turn"));

      try {
        // Try to call turn methods directly
        const currentTurnPage = $flipbook.turn("page");
        // console.log(`Current page: ${currentTurnPage}`);

        if (currentTurnPage > 1) {
          $flipbook.turn("previous");
          // console.log("Called turn previous");
        }
      } catch (error) {
        console.error("Error in prevPage:", error);
      }
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event) => {
      switch (event.key) {
        case "ArrowLeft":
          event.preventDefault();
          prevPage();
          break;
        case "ArrowRight":
          event.preventDefault();
          nextPage();
          break;
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, []);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center relative">
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p
              className={`${cormorant.className} text-white/70 text-lg font-light tracking-[0.1em]`}
              style={{
                textShadow: "0 0 3px rgba(255, 255, 255, 0.3)",
              }}
            >
              Loading Portfolio...
            </p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center z-10">
          <div className="text-center p-8">
            <p
              className={`${cormorant.className} text-white/70 text-lg font-light tracking-[0.1em]`}
              style={{
                textShadow: "0 0 3px rgba(255, 255, 255, 0.3)",
              }}
            >
              {error}
            </p>
            <p className="text-white/50 text-sm mt-2 mb-4">
              Please check if the images exist and are accessible.
            </p>
            <button
              onClick={() => {
                setError(null);
                setIsLoading(true);
                window.location.reload();
              }}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-all duration-1000 border border-white/20"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Turn.js Flipbook */}
      {!isLoading && !error && portfolioPages.length > 0 && (
        <div className="flex-1 flex items-center justify-center overflow-hidden w-full relative">
          {/* Left Navigation Button */}
          <button
            onClick={prevPage}
            disabled={currentPage === 0}
            className="absolute -left-1 z-[999] disabled:opacity-50 disabled:cursor-not-allowed text-white md:hover:text-white/50 transition-all duration-1000"
            aria-label="Previous page"
          >
            <ChevronLeft className="w-16 h-16" />
          </button>

          <div className="flipbook-container transition-all duration-1000">
            <div
              ref={flipbookRef}
              id="flipbook"
              className="flipbook transition-all duration-1000"
              style={{
                width: `${
                  isDualPageMode()
                    ? dualPageDimensions.width
                    : pageDimensions.width
                }px`,
                height: `${
                  isDualPageMode()
                    ? dualPageDimensions.height
                    : pageDimensions.height
                }px`,
                margin: "0 auto", // Always center the flipbook
              }}
            >
              {portfolioPages.map((page, index) => (
                <div
                  key={index}
                  data-page={index + 1}
                  style={{
                    // background: "white",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    overflow: "hidden",
                    width: "100%",
                    height: "100%",
                  }}
                >
                  <img
                    src={page.imageUrl}
                    alt={`Portfolio page ${page.pageNumber}`}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "contain",
                      display: "block",
                    }}
                    onError={(e) => {
                      console.error(`Failed to load image: ${page.imageUrl}`);
                      e.target.style.display = "none";
                    }}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Right Navigation Button */}
          <button
            onClick={nextPage}
            disabled={currentPage === portfolioPages.length - 1}
            className="absolute -right-1 z-[999] disabled:opacity-50 disabled:cursor-not-allowed text-white md:hover:text-white/50 transition-all duration-1000"
            aria-label="Next page"
          >
            <ChevronRight className="w-16 h-16" />
          </button>
        </div>
      )}

      {/* Page Counter */}
      {!isLoading && !error && portfolioPages.length > 0 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-4 bg-black/30 backdrop-blur-sm rounded-full px-6 py-3 border border-white/10">
          <div className="flex items-center gap-2">
            <span
              className={`${cormorant.className} text-white text-sm font-light tracking-[0.1em]`}
              style={{
                textShadow: "0 0 3px rgba(255, 255, 255, 0.5)",
              }}
            >
              {currentPage + 1} / {portfolioPages.length}
            </span>
          </div>
        </div>
      )}

      {/* Keyboard Navigation Hint */}
      {!isLoading && !error && !isMobile && portfolioPages.length > 0 && (
        <div className="absolute top-4 right-4 text-white/50 text-xs">
          <p className={`${cormorant.className} font-light tracking-[0.1em]`}>
            Use ← → keys to navigate pages
          </p>
        </div>
      )}
    </div>
  );
}
