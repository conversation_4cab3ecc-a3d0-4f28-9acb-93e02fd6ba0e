"use client";

import { useEffect, useRef } from "react";

export default function EnhancedClickEffect() {
  const canvasRef = useRef(null);
  const clickParticlesRef = useRef([]);
  const ambientParticlesRef = useRef([]);
  const animationFrameRef = useRef(null);
  const lastAmbientSpawnRef = useRef(0);
  const lastCenterSpawnRef = useRef(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");

    // Set canvas to full window size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener("resize", resizeCanvas);

    // Particle class with constantly changing opacity and extremely slow movement
    class Particle {
      constructor(x, y, angle, isAmbient = false, isCenter = false) {
        this.x = x;
        this.y = y;
        this.size = Math.random() * 1.5 + 0.8; // 0.8-2.3px size

        // Use angle for direction (in radians)
        this.angle = angle;

        // Ultra-slow speed (significantly reduced)
        this.speed = isAmbient
          ? Math.random() * 0.06 + 0.02 // 0.02-0.08 for ambient
          : Math.random() * 0.08 + 0.04; // 0.04-0.12 for click particles

        // Opacity properties for continuous change
        this.baseOpacity = Math.random() * 0.25 + 0.1; // Base opacity between 0.1-0.35
        this.opacityVariation = Math.random() * 0.15 + 0.05; // Variation amount
        this.opacitySpeed = Math.random() * 0.01 + 0.005; // Speed of opacity change
        this.opacityOffset = Math.random() * Math.PI * 2; // Random starting point in the cycle

        // Track distance traveled for removal - greatly increased max distance
        this.distanceTraveled = 0;
        this.maxDistance = Math.random() * 1000 + 1500; // 1500-2500px

        // Flags for particle type
        this.isAmbient = isAmbient;
        this.isCenter = isCenter;
      }

      update(canvasWidth, canvasHeight) {
        // Move based on angle (linear movement)
        const dx = Math.cos(this.angle) * this.speed;
        const dy = Math.sin(this.angle) * this.speed;

        this.x += dx;
        this.y += dy;

        // Track distance traveled
        this.distanceTraveled += Math.sqrt(dx * dx + dy * dy);

        // Calculate current opacity using sine wave for continuous change
        // This creates a gentle pulsing/twinkling effect
        const time = performance.now() * 0.001; // Current time in seconds
        const opacityCycle = Math.sin(
          time * this.opacitySpeed + this.opacityOffset
        );
        this.opacity = this.baseOpacity + opacityCycle * this.opacityVariation;

        // Ensure opacity stays positive
        if (this.opacity < 0.05) this.opacity = 0.05;

        // Check if particle is still on screen and hasn't traveled too far
        // Extended the boundary check to allow particles to travel further off-screen
        return (
          this.x > -50 &&
          this.x < canvasWidth + 50 &&
          this.y > -50 &&
          this.y < canvasHeight + 50 &&
          this.distanceTraveled < this.maxDistance
        );
      }

      draw(ctx) {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${this.opacity})`;
        ctx.fill();
      }
    }

    // Handle click to create particles with scattered directions
    const handleClick = (e) => {
      // Create 4 particles with random angles
      for (let i = 0; i < 4; i++) {
        // Generate a truly random angle (0-2π radians)
        const angle = Math.random() * Math.PI * 2;

        clickParticlesRef.current.push(
          new Particle(e.clientX, e.clientY, angle)
        );
      }
    };

    // Function to spawn ambient particles from edges
    const spawnAmbientParticle = () => {
      // Decide which edge to spawn from
      const edge = Math.floor(Math.random() * 4); // 0: top, 1: right, 2: bottom, 3: left

      let x, y, angle;

      switch (edge) {
        case 0: // Top edge
          x = Math.random() * canvas.width;
          y = -5;
          angle = Math.random() * Math.PI + Math.PI / 2; // Downward direction with variation
          break;
        case 1: // Right edge
          x = canvas.width + 5;
          y = Math.random() * canvas.height;
          angle = Math.random() * Math.PI + Math.PI; // Leftward direction with variation
          break;
        case 2: // Bottom edge
          x = Math.random() * canvas.width;
          y = canvas.height + 5;
          angle = Math.random() * Math.PI - Math.PI / 2; // Upward direction with variation
          break;
        case 3: // Left edge
          x = -5;
          y = Math.random() * canvas.height;
          angle = Math.random() * Math.PI; // Rightward direction with variation
          break;
      }

      // Add some randomness to the angle
      angle += (Math.random() - 0.5) * 0.5;

      ambientParticlesRef.current.push(new Particle(x, y, angle, true));
    };

    // Function to spawn particles from center areas of the screen
    const spawnCenterParticle = () => {
      // Create particles in the central area of the screen
      const centerMargin = 0.3; // 30% margin from edges

      // Random position within the central area
      const x =
        canvas.width * (centerMargin + Math.random() * (1 - 2 * centerMargin));
      const y =
        canvas.height * (centerMargin + Math.random() * (1 - 2 * centerMargin));

      // Random angle for direction
      const angle = Math.random() * Math.PI * 2;

      ambientParticlesRef.current.push(new Particle(x, y, angle, true, true));
    };

    // Function to create initial ambient particles
    const createInitialParticles = () => {
      // Create particles at random positions across the screen
      const initialCount = 25; // Start with 25 particles

      for (let i = 0; i < initialCount; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const angle = Math.random() * Math.PI * 2;

        ambientParticlesRef.current.push(new Particle(x, y, angle, true));
      }
    };

    // Create initial particles
    createInitialParticles();

    // Animation loop
    const animate = (timestamp) => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Spawn ambient particles from edges
      if (timestamp - lastAmbientSpawnRef.current > 800) {
        // Every 0.8 seconds
        if (ambientParticlesRef.current.length < 40) {
          // Spawn 1-3 particles at once
          const spawnCount = Math.floor(Math.random() * 3) + 1;
          for (let i = 0; i < spawnCount; i++) {
            spawnAmbientParticle();
          }
        }
        lastAmbientSpawnRef.current = timestamp;
      }

      // Spawn particles from center areas
      if (timestamp - lastCenterSpawnRef.current > 1500) {
        // Every 1.5 seconds
        // Ensure we always maintain a minimum number of particles
        if (ambientParticlesRef.current.length < 25) {
          // Spawn 2-4 particles from center areas
          const spawnCount = Math.floor(Math.random() * 3) + 2;
          for (let i = 0; i < spawnCount; i++) {
            spawnCenterParticle();
          }
        }
        lastCenterSpawnRef.current = timestamp;
      }

      // Update and draw click particles
      clickParticlesRef.current = clickParticlesRef.current.filter(
        (particle) => {
          const isActive = particle.update(canvas.width, canvas.height);
          if (isActive) {
            particle.draw(ctx);
          }
          return isActive;
        }
      );

      // Update and draw ambient particles
      ambientParticlesRef.current = ambientParticlesRef.current.filter(
        (particle) => {
          const isActive = particle.update(canvas.width, canvas.height);
          if (isActive) {
            particle.draw(ctx);
          }
          return isActive;
        }
      );

      // Check if we need to repopulate particles
      if (ambientParticlesRef.current.length < 15) {
        // If particles have dropped too low, add a burst of new ones
        for (let i = 0; i < 10; i++) {
          spawnCenterParticle();
        }
      }

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    window.addEventListener("click", handleClick);
    animate(0);

    return () => {
      window.removeEventListener("resize", resizeCanvas);
      window.removeEventListener("click", handleClick);
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, []);

  return (
    <canvas ref={canvasRef} className="pointer-events-none fixed inset-0 " />
  );
}
